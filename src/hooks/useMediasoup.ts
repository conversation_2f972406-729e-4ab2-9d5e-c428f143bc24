import { useRef, useCallback, useState } from 'react';
import { Device, types } from 'mediasoup-client';
import { io, Socket } from 'socket.io-client';

interface MediasoupState {
  isConnected: boolean;
  isProducing: boolean;
  isStreaming: boolean;
  error: string | null;
}

interface UseMediasoupReturn {
  state: MediasoupState;
  connect: () => Promise<void>;
  startProducing: (stream: MediaStream) => Promise<void>;
  stopProducing: () => Promise<void>;
  startRTMPStream: () => Promise<void>;
  stopRTMPStream: () => Promise<void>;
  disconnect: () => void;
}

export const useMediasoup = (): UseMediasoupReturn => {
  const [state, setState] = useState<MediasoupState>({
    isConnected: false,
    isProducing: false,
    isStreaming: false,
    error: null,
  });

  const deviceRef = useRef<Device | null>(null);
  const transportRef = useRef<types.Transport | null>(null);
  const producerRef = useRef<types.Producer | null>(null);
  const audioProducerRef = useRef<types.Producer | null>(null);
  const socketRef = useRef<Socket | null>(null);


  const updateState = useCallback((updates: Partial<MediasoupState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const connect = useCallback(async () => {
    try {
      updateState({ error: null });

      // Connect to media server
      const mediaServerUrl = import.meta.env.VITE_MEDIA_SERVER_URL || 'http://localhost:8081';
      console.log('🔗 Attempting to connect to media server:', mediaServerUrl);

      const socket = io(mediaServerUrl, {
        transports: ['websocket', 'polling']
      });
      socketRef.current = socket;

      // Listen for streaming status updates
      socket.on('streaming-status', ({ isStreaming }: { isStreaming: boolean }) => {
        updateState({ isStreaming });
        console.log('📡 Streaming status updated:', isStreaming ? 'Started' : 'Stopped');
      });

      // Wait for connection
      await new Promise<void>((resolve, reject) => {
        socket.on('connect', () => {
          console.log('✅ Connected to media server');
          resolve();
        });
        socket.on('connect_error', (error) => {
          console.error('❌ Connection error:', error);
          reject(error);
        });
        socket.on('disconnect', (reason) => {
          console.log('🔌 Disconnected from media server:', reason);
        });
        setTimeout(() => reject(new Error('Connection timeout')), 10000);
      });

      // Create mediasoup device
      const device = new Device();
      deviceRef.current = device;

      // Get transport info from server
      const transportInfo = await new Promise<any>((resolve, reject) => {
        socket.emit('get-transport-info');
        socket.on('transport-info', resolve);
        socket.on('error', (error: any) => reject(new Error(error.message)));
        setTimeout(() => reject(new Error('Transport info timeout')), 10000);
      });

      // Load device with router RTP capabilities
      await device.load({
        routerRtpCapabilities: transportInfo.routerRtpCapabilities || {
          codecs: [
            {
              kind: 'audio',
              mimeType: 'audio/opus',
              preferredPayloadType: 111,
              clockRate: 48000,
              channels: 2,
              parameters: {
                minptime: 10,
                useinbandfec: 1,
              },
            },
            {
              kind: 'video',
              mimeType: 'video/H264',
              preferredPayloadType: 125,
              clockRate: 90000,
              parameters: {
                'level-asymmetry-allowed': 1,
                'packetization-mode': 1,
                'profile-level-id': '42e01f',
              },
            },
          ],
          headerExtensions: [],
        },
      });

      // Create send transport
      const transport = device.createSendTransport({
        id: transportInfo.id,
        iceParameters: transportInfo.iceParameters,
        iceCandidates: transportInfo.iceCandidates,
        dtlsParameters: transportInfo.dtlsParameters,
      });
      transportRef.current = transport;

      // Handle transport connection
      transport.on('connect', async ({ dtlsParameters }, callback, errback) => {
        try {
          socket.emit('connect-transport', { dtlsParameters });
          await new Promise<void>((resolve, reject) => {
            socket.on('transport-connected', resolve);
            socket.on('error', (error: any) => reject(new Error(error.message)));
            setTimeout(() => reject(new Error('Transport connect timeout')), 10000);
          });
          callback();
        } catch (error) {
          errback(error as Error);
        }
      });

      // Handle producer creation
      transport.on('produce', async ({ kind, rtpParameters }, callback, errback) => {
        try {
          const { producerId } = await new Promise<any>((resolve, reject) => {
            socket.emit('create-producer', { kind, rtpParameters });
            socket.on('producer-created', resolve);
            socket.on('error', (error: any) => reject(new Error(error.message)));
            setTimeout(() => reject(new Error('Producer creation timeout')), 10000);
          });
          callback({ id: producerId });
        } catch (error) {
          errback(error as Error);
        }
      });

      updateState({ isConnected: true });
      console.log('✅ Connected to media server');
    } catch (error) {
      console.error('❌ Failed to connect to media server:', error);
      updateState({
        error: error instanceof Error ? error.message : 'Failed to connect to media server',
        isConnected: false
      });
      throw error;
    }
  }, [updateState]);

  const startProducing = useCallback(async (canvasStream: MediaStream) => {
    try {
      if (!deviceRef.current || !transportRef.current) {
        throw new Error('Device or transport not initialized');
      }

      updateState({ error: null });

      // Get microphone access for audio
      let microphoneStream: MediaStream | null = null;
      try {
        microphoneStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
          },
          video: false,
        });
        console.log('✅ Microphone access granted');
      } catch (error) {
        console.warn('⚠️ Microphone access denied, proceeding with video only:', error);
      }

      // Create audio producer if microphone is available
      if (microphoneStream) {
        const audioTrack = microphoneStream.getAudioTracks()[0];
        if (audioTrack) {
          const audioProducer = await transportRef.current.produce({
            track: audioTrack,
          });

          audioProducerRef.current = audioProducer;

          audioProducer.on('transportclose', () => {
            console.log('Audio producer transport closed');
          });

          audioProducer.on('trackended', () => {
            console.log('Audio producer track ended');
          });

          console.log('✅ Started producing audio stream');
          console.log('📊 Audio producer details:', {
            id: audioProducer.id,
            kind: audioProducer.kind,
            paused: audioProducer.paused,
          });
        }
      }

      // Create video producer from canvas stream
      const videoTrack = canvasStream.getVideoTracks()[0];
      if (!videoTrack) {
        throw new Error('No video track found in canvas stream');
      }

      const videoProducer = await transportRef.current.produce({
        track: videoTrack,
        encodings: [
          {
            maxBitrate: 3000000, // 3Mbps
            maxFramerate: 30,
          },
        ],
        codecOptions: {
          videoGoogleStartBitrate: 3000, // 3Mbps in kbps
        },
      });

      producerRef.current = videoProducer;

      videoProducer.on('transportclose', () => {
        console.log('Video producer transport closed');
        updateState({ isProducing: false });
      });

      videoProducer.on('trackended', () => {
        console.log('Video producer track ended');
        updateState({ isProducing: false });
      });

      updateState({ isProducing: true });

      console.log('✅ Started producing video stream');
      console.log('📊 Video producer details:', {
        id: videoProducer.id,
        kind: videoProducer.kind,
        paused: videoProducer.paused,
        track: {
          kind: videoTrack.kind,
          enabled: videoTrack.enabled,
          readyState: videoTrack.readyState,
          settings: videoTrack.getSettings?.()
        }
      });

      // Ensure video producer is not paused
      if (videoProducer.paused) {
        console.log('🔄 Video producer was paused, resuming...');
        videoProducer.resume();
        console.log('✅ Video producer resumed, paused:', videoProducer.paused);
      }

      // Monitor video producer stats every 5 seconds
      const statsInterval = setInterval(async () => {
        try {
          const stats = await videoProducer.getStats();
          console.log('📊 Video producer stats:', Array.from(stats.values()));
        } catch (error) {
          console.log('⚠️ Failed to get video producer stats:', error);
        }
      }, 5000);

      // Clean up stats monitoring when producer is closed
      videoProducer.on('@close', () => {
        clearInterval(statsInterval);
        console.log('🔌 Video producer closed');
      });
    } catch (error) {
      console.error('❌ Failed to start producing:', error);
      updateState({ 
        error: error instanceof Error ? error.message : 'Failed to start producing',
        isProducing: false 
      });
      throw error;
    }
  }, [updateState]);

  const stopProducing = useCallback(async () => {
    try {
      if (audioProducerRef.current) {
        audioProducerRef.current.close();
        audioProducerRef.current = null;
        console.log('⏹️ Stopped producing audio stream');
      }

      if (producerRef.current) {
        producerRef.current.close();
        producerRef.current = null;
        console.log('⏹️ Stopped producing video stream');
      }

      updateState({ isProducing: false });
    } catch (error) {
      console.error('❌ Failed to stop producing:', error);
      updateState({
        error: error instanceof Error ? error.message : 'Failed to stop producing'
      });
    }
  }, [updateState]);

  const startRTMPStream = useCallback(async () => {
    try {
      if (!socketRef.current) {
        throw new Error('Socket not initialized');
      }

      updateState({ error: null });

      // Check if producer exists (RTMP should already be streaming)
      if (producerRef.current) {
        // Request current streaming status from server
        const streamingStatus = await new Promise<{ isStreaming: boolean }>((resolve, reject) => {
          socketRef.current!.emit('get-streaming-status');
          socketRef.current!.on('streaming-status', resolve);
          socketRef.current!.on('error', (error: any) => reject(new Error(error.message)));
          setTimeout(() => reject(new Error('Streaming status timeout')), 5000);
        });

        updateState({ isStreaming: streamingStatus.isStreaming });
        console.log('✅ RTMP streaming status:', streamingStatus.isStreaming ? 'Active' : 'Inactive');
      } else {
        throw new Error('No video producer available. Start video production first.');
      }
    } catch (error) {
      console.error('❌ Failed to get RTMP stream status:', error);
      updateState({
        error: error instanceof Error ? error.message : 'Failed to get RTMP stream status',
        isStreaming: false
      });
      throw error;
    }
  }, [updateState]);

  const stopRTMPStream = useCallback(async () => {
    try {
      if (!socketRef.current) {
        throw new Error('Socket not initialized');
      }

      await new Promise<any>((resolve, reject) => {
        socketRef.current!.emit('stop-rtmp');
        socketRef.current!.on('rtmp-status', resolve);
        socketRef.current!.on('error', (error: any) => reject(new Error(error.message)));
        setTimeout(() => reject(new Error('RTMP stop timeout')), 10000);
      });

      updateState({ isStreaming: false });
      console.log('⏹️ RTMP streaming stopped');
    } catch (error) {
      console.error('❌ Failed to stop RTMP stream:', error);
      updateState({ 
        error: error instanceof Error ? error.message : 'Failed to stop RTMP stream' 
      });
    }
  }, [updateState]);

  const disconnect = useCallback(() => {
    try {
      if (producerRef.current) {
        producerRef.current.close();
        producerRef.current = null;
      }

      if (transportRef.current) {
        transportRef.current.close();
        transportRef.current = null;
      }

      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
      }

      deviceRef.current = null;

      updateState({
        isConnected: false,
        isProducing: false,
        isStreaming: false,
        error: null,
      });

      console.log('🔌 Disconnected from mediasoup');
    } catch (error) {
      console.error('❌ Error during disconnect:', error);
    }
  }, [updateState]);

  return {
    state,
    connect,
    startProducing,
    stopProducing,
    startRTMPStream,
    stopRTMPStream,
    disconnect,
  };
};
