import React, { useState, useCallback, useEffect } from 'react';
import { useMediasoup } from '../hooks/useMediasoup';

interface RTMPStreamingProps {
  canvasStream: MediaStream | null;
  isHost: boolean;
}

export const RTMPStreaming: React.FC<RTMPStreamingProps> = ({
  canvasStream,
  isHost,
}) => {

  const {
    state,
    connect,
    startProducing,
    stopProducing,
    startRTMPStream,
    stopRTMPStream,
    disconnect,
  } = useMediasoup();

  // Manual connection only - no auto-reconnect to prevent infinite loops
  // useEffect(() => {
  //   if (isHost && !state.isConnected) {
  //     connect().catch(console.error);
  //   }

  //   return () => {
  //     disconnect();
  //   };
  // }, [isHost, state.isConnected, connect, disconnect]);

  // Note: Removed auto-start producing to prevent infinite loops
  // Production will be started manually via the "Start Streaming" button

  const handleStartStreaming = useCallback(async () => {
    try {
      // If not producing yet, start producing (which will auto-start RTMP)
      if (!state.isProducing && canvasStream) {
        console.log('🎬 Starting video production...');
        await startProducing(canvasStream);
      } else if (state.isProducing && !state.isStreaming) {
        // If producing but not streaming, trigger streaming
        await startRTMPStream();
      } else {
        console.log('ℹ️ Streaming already active or no canvas stream available');
      }
    } catch (error) {
      console.error('Failed to start RTMP streaming:', error);
    }
  }, [startRTMPStream, startProducing, canvasStream, state.isProducing, state.isStreaming]);

  const handleStopStreaming = useCallback(async () => {
    try {
      await stopRTMPStream();
    } catch (error) {
      console.error('Failed to stop RTMP streaming:', error);
    }
  }, [stopRTMPStream]);

  if (!isHost) {
    return null; // Only hosts can control RTMP streaming
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold mb-4">RTMP Streaming</h3>

      {/* Connection Status */}
      <div className="mb-4">
        <div className="flex items-center space-x-2 mb-2">
          <div
            className={`w-3 h-3 rounded-full ${
              state.isConnected ? 'bg-green-500' : 'bg-red-500'
            }`}
          />
          <span className="text-sm">
            Mediasoup: {state.isConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
        <div className="flex items-center space-x-2 mb-2">
          <div
            className={`w-3 h-3 rounded-full ${
              state.isProducing ? 'bg-green-500' : 'bg-gray-400'
            }`}
          />
          <span className="text-sm">
            Video Producer: {state.isProducing ? 'Active' : 'Inactive'}
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <div
            className={`w-3 h-3 rounded-full ${
              state.isStreaming ? 'bg-red-500' : 'bg-gray-400'
            }`}
          />
          <span className="text-sm">
            RTMP Stream: {state.isStreaming ? 'Live' : 'Offline'}
          </span>
        </div>
      </div>

      {/* Error Display */}
      {state.error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <strong>Error:</strong> {state.error}
        </div>
      )}

      {/* RTMP Configuration Info */}
      <div className="mb-4 p-3 bg-blue-50 rounded-md">
        <div className="text-sm font-medium text-blue-900 mb-1">RTMP Configuration</div>
        <div className="text-xs text-blue-700">
          Streaming to: rtmp://live.twitch.tv/app (hardcoded in media server)
        </div>
      </div>

      {/* Connection Controls */}
      <div className="space-y-3">
        {!state.isConnected ? (
          <button
            onClick={() => connect().catch(console.error)}
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            🔗 Connect to Media Server
          </button>
        ) : (
          <div className="text-sm text-green-600 bg-green-50 p-2 rounded">
            ✅ Connected to media server
          </div>
        )}

        {/* Stream Controls */}
        <div className="flex space-x-2">
          {!state.isStreaming ? (
            <button
              onClick={handleStartStreaming}
              disabled={!canvasStream || !state.isConnected}
              className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              🔴 {state.isProducing ? 'Start Streaming' : 'Start Production & Streaming'}
            </button>
          ) : (
            <button
              onClick={handleStopStreaming}
              className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              ⏹️ Stop Streaming
            </button>
          )}
        </div>

        {!canvasStream && (
          <div className="text-sm text-amber-600 bg-amber-50 p-2 rounded">
            ⚠️ No canvas stream available. Make sure the video compositor is running.
          </div>
        )}

        {canvasStream && !state.isConnected && (
          <div className="text-sm text-amber-600 bg-amber-50 p-2 rounded">
            ⚠️ Not connected to media server. Attempting to reconnect...
          </div>
        )}
      </div>

      {/* Help Text */}
      <div className="mt-4 text-xs text-gray-500">
        <p>
          Configure your RTMP endpoint (e.g., Twitch, YouTube, etc.) and start streaming
          your WebGL composite video output.
        </p>
      </div>
    </div>
  );
};
