import React, { useCallback, useEffect, useState } from 'react';
import { useMediasoup } from '../hooks/useMediasoup';

interface RTMPStreamingProps {
  canvasStream: MediaStream | null;
  isHost: boolean;
  onRestartCanvasStream?: () => void;
}

export const RTMPStreaming: React.FC<RTMPStreamingProps> = ({
  canvasStream,
  isHost,
  onRestartCanvasStream,
}) => {

  const {
    state,
    connect,
    startProducing,
    stopProducing,
    startRTMPStream,
    stopRTMPStream,
    disconnect,
  } = useMediasoup();

  const [isStarting, setIsStarting] = useState(false);
  const [isStopping, setIsStopping] = useState(false);

  // Auto-connect when component mounts if host
  useEffect(() => {
    if (isHost && !state.isConnected) {
      connect().catch(console.error);
    }
  }, [isHost, state.isConnected, connect]);

  const handleStartStreaming = useCallback(async () => {
    if (isStarting) return; // Prevent double-clicks

    setIsStarting(true);
    try {
      // Connect first if not connected
      if (!state.isConnected) {
        console.log('🔗 Connecting to media server...');
        await connect();
      }

      // Always restart canvas stream to ensure fresh tracks
      console.log('🔄 Ensuring fresh canvas stream...');
      if (onRestartCanvasStream) {
        onRestartCanvasStream();
        // Wait longer for the new stream to be properly initialized
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Verify we have a valid canvas stream
      if (!canvasStream) {
        throw new Error('Canvas stream is not available. Please refresh the page.');
      }

      // Double-check track states
      const tracks = canvasStream.getTracks();
      const hasEndedTracks = tracks.some(track => track.readyState === 'ended');

      if (hasEndedTracks) {
        console.log('⚠️ Still have ended tracks after restart, trying once more...');
        if (onRestartCanvasStream) {
          onRestartCanvasStream();
          await new Promise(resolve => setTimeout(resolve, 500));
        }

        // Final check
        if (canvasStream.getTracks().some(track => track.readyState === 'ended')) {
          throw new Error('Unable to get fresh canvas stream. Please refresh the page.');
        }
      }

      // Start producing (RTMP will auto-start when video producer is created)
      if (!state.isProducing) {
        console.log('🎬 Starting video production (RTMP will auto-start)...');
        await startProducing(canvasStream);
      }
    } catch (error) {
      console.error('Failed to start streaming:', error);
    } finally {
      setIsStarting(false);
    }
  }, [connect, startProducing, canvasStream, state.isConnected, state.isProducing, onRestartCanvasStream, isStarting]);

  const handleStopStreaming = useCallback(async () => {
    if (isStopping) return; // Prevent double-clicks

    setIsStopping(true);
    try {
      // Stop RTMP streaming
      if (state.isStreaming) {
        console.log('⏹️ Stopping RTMP stream...');
        await stopRTMPStream();
      }

      // Stop producing
      if (state.isProducing) {
        console.log('⏹️ Stopping video production...');
        await stopProducing();
      }
    } catch (error) {
      console.error('Failed to stop streaming:', error);
    } finally {
      setIsStopping(false);
    }
  }, [stopRTMPStream, stopProducing, state.isStreaming, state.isProducing, isStopping]);

  if (!isHost) {
    return null; // Only hosts can control RTMP streaming
  }

  const isStreaming = state.isStreaming;
  const canStream = canvasStream && state.isConnected;

  return (
    <div className="bg-gray-800/50 backdrop-blur-lg border border-gray-700 rounded-2xl p-4">
      <h3 className="text-lg font-bold text-white mb-4">RTMP Streaming</h3>

      {/* Streaming Status */}
      <div className="mb-4">
        <div className="flex items-center space-x-3">
          <div
            className={`w-4 h-4 rounded-full ${
              isStreaming ? 'bg-red-500 animate-pulse' : 'bg-gray-500'
            }`}
          />
          <span className="text-white font-medium">
            {isStreaming ? 'Streaming' : 'Not Streaming'}
          </span>
        </div>
      </div>

      {/* Error Display */}
      {state.error && (
        <div className="mb-4 p-3 bg-red-500/20 border border-red-500/50 text-red-300 rounded-lg">
          <strong>Error:</strong> {state.error}
        </div>
      )}

      {/* Stream Controls */}
      <div className="space-y-3">
        {!isStreaming ? (
          <button
            onClick={handleStartStreaming}
            disabled={!canvasStream || isStarting}
            className="w-full px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:bg-gray-600 disabled:cursor-not-allowed font-medium transition-colors flex items-center justify-center space-x-2"
          >
            {isStarting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Starting...</span>
              </>
            ) : (
              <>
                <span>🔴</span>
                <span>Start Streaming</span>
              </>
            )}
          </button>
        ) : (
          <button
            onClick={handleStopStreaming}
            disabled={isStopping}
            className="w-full px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 font-medium transition-colors flex items-center justify-center space-x-2"
          >
            {isStopping ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Stopping...</span>
              </>
            ) : (
              <>
                <span>⏹️</span>
                <span>Stop Streaming</span>
              </>
            )}
          </button>
        )}

        {!canvasStream && (
          <div className="text-sm text-amber-300 bg-amber-500/20 p-3 rounded-lg">
            ⚠️ No video composite available. Make sure the video compositor is running.
          </div>
        )}

        {!state.isConnected && (
          <div className="text-sm text-blue-300 bg-blue-500/20 p-3 rounded-lg">
            🔗 Connecting to media server...
          </div>
        )}
      </div>

      {/* RTMP Configuration Info */}
      <div className="mt-4 p-3 bg-blue-500/20 rounded-lg">
        <div className="text-sm font-medium text-blue-300 mb-1">RTMP Configuration</div>
        <div className="text-xs text-blue-400">
          Streaming to: https://www.twitch.tv/mikeswitcherstudio
        </div>
      </div>
    </div>
  );
};
