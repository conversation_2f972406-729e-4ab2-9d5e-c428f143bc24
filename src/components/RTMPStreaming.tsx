import React, { useCallback, useEffect } from 'react';
import { useMediasoup } from '../hooks/useMediasoup';

interface RTMPStreamingProps {
  canvasStream: MediaStream | null;
  isHost: boolean;
}

export const RTMPStreaming: React.FC<RTMPStreamingProps> = ({
  canvasStream,
  isHost,
}) => {

  const {
    state,
    connect,
    startProducing,
    stopProducing,
    startRTMPStream,
    stopRTMPStream,
    disconnect,
  } = useMediasoup();

  // Auto-connect when component mounts if host
  useEffect(() => {
    if (isHost && !state.isConnected) {
      connect().catch(console.error);
    }
  }, [isHost, state.isConnected, connect]);

  const handleStartStreaming = useCallback(async () => {
    try {
      // Connect first if not connected
      if (!state.isConnected) {
        console.log('🔗 Connecting to media server...');
        await connect();
      }

      // Start producing if not already producing
      if (!state.isProducing && canvasStream) {
        console.log('🎬 Starting video production...');
        await startProducing(canvasStream);
      }

      // Start RTMP streaming if not already streaming
      if (!state.isStreaming) {
        console.log('📡 Starting RTMP stream...');
        await startRTMPStream();
      }
    } catch (error) {
      console.error('Failed to start streaming:', error);
    }
  }, [connect, startProducing, startRTMPStream, canvasStream, state.isConnected, state.isProducing, state.isStreaming]);

  const handleStopStreaming = useCallback(async () => {
    try {
      // Stop RTMP streaming
      if (state.isStreaming) {
        console.log('⏹️ Stopping RTMP stream...');
        await stopRTMPStream();
      }

      // Stop producing
      if (state.isProducing) {
        console.log('⏹️ Stopping video production...');
        await stopProducing();
      }
    } catch (error) {
      console.error('Failed to stop streaming:', error);
    }
  }, [stopRTMPStream, stopProducing, state.isStreaming, state.isProducing]);

  if (!isHost) {
    return null; // Only hosts can control RTMP streaming
  }

  const isStreaming = state.isStreaming;
  const canStream = canvasStream && state.isConnected;

  return (
    <div className="bg-gray-800/50 backdrop-blur-lg border border-gray-700 rounded-2xl p-4">
      <h3 className="text-lg font-bold text-white mb-4">RTMP Streaming</h3>

      {/* Streaming Status */}
      <div className="mb-4">
        <div className="flex items-center space-x-3">
          <div
            className={`w-4 h-4 rounded-full ${
              isStreaming ? 'bg-red-500 animate-pulse' : 'bg-gray-500'
            }`}
          />
          <span className="text-white font-medium">
            {isStreaming ? 'Streaming' : 'Not Streaming'}
          </span>
        </div>
      </div>

      {/* Error Display */}
      {state.error && (
        <div className="mb-4 p-3 bg-red-500/20 border border-red-500/50 text-red-300 rounded-lg">
          <strong>Error:</strong> {state.error}
        </div>
      )}

      {/* Stream Controls */}
      <div className="space-y-3">
        {!isStreaming ? (
          <button
            onClick={handleStartStreaming}
            disabled={!canvasStream}
            className="w-full px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:bg-gray-600 disabled:cursor-not-allowed font-medium transition-colors"
          >
            🔴 Start Streaming
          </button>
        ) : (
          <button
            onClick={handleStopStreaming}
            className="w-full px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 font-medium transition-colors"
          >
            ⏹️ Stop Streaming
          </button>
        )}

        {!canvasStream && (
          <div className="text-sm text-amber-300 bg-amber-500/20 p-3 rounded-lg">
            ⚠️ No video composite available. Make sure the video compositor is running.
          </div>
        )}

        {!state.isConnected && (
          <div className="text-sm text-blue-300 bg-blue-500/20 p-3 rounded-lg">
            🔗 Connecting to media server...
          </div>
        )}
      </div>

      {/* RTMP Configuration Info */}
      <div className="mt-4 p-3 bg-blue-500/20 rounded-lg">
        <div className="text-sm font-medium text-blue-300 mb-1">RTMP Configuration</div>
        <div className="text-xs text-blue-400">
          Streaming to: rtmp://live.twitch.tv/app
        </div>
      </div>
    </div>
  );
};
