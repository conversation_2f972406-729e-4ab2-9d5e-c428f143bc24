module.exports = {
  https: {
    cert: process.env.SSL_CERT_PATH || "cert.pem",
    certKey: process.env.SSL_KEY_PATH || "key.pem",
    port: parseInt(process.env.PORT) || 8080,
    wsPath: "/server",
    wsPingInterval: 25000,
    wsPingTimeout: 5000,
  },

  mediasoup: {
    // WorkerSettings
    worker: {
      logLevel: process.env.MEDIASOUP_LOG_LEVEL || "warn", // Less verbose in production
      logTags: [
        "dtls",
        "ice",
        "info",
        "rtcp",
        "rtp",
        "srtp",
      ],
      rtcMinPort: parseInt(process.env.RTC_MIN_PORT) || 32256,
      rtcMaxPort: parseInt(process.env.RTC_MAX_PORT) || 65535,
    },

    // RouterOptions
    router: {
      // RtpCodecCapability[]
      mediaCodecs: [
        {
          kind: "audio",
          mimeType: "audio/opus",
          preferredPayloadType: 111,
          clockRate: 48000,
          channels: 2,
          parameters: {
            minptime: 10,
            useinbandfec: 1,
          },
        },
        {
          kind: "video",
          mimeType: "video/H264",
          preferredPayloadType: 125,
          clockRate: 90000,
          parameters: {
            "level-asymmetry-allowed": 1,
            "packetization-mode": 1,
            "profile-level-id": "42e01f",
          },
        },
      ],
    },

    // WebRtcTransportOptions
    webrtcTransport: {
      listenIps: [{ 
        ip: process.env.LISTEN_IP || "0.0.0.0", 
        announcedIp: process.env.ANNOUNCED_IP || null 
      }],
      enableUdp: true,
      enableTcp: true,
      preferUdp: true,
      initialAvailableOutgoingBitrate: 300000,
    },

    // PlainTransportOptions
    plainTransport: {
      listenIp: { 
        ip: process.env.LISTEN_IP || "0.0.0.0", 
        announcedIp: process.env.ANNOUNCED_IP || null 
      },
    },

    client: {
      // ProducerOptions
      videoProducer: {
        // Send video with single stream (no simulcast)
        // RTCRtpEncodingParameters[]
        encodings: [
          {
            maxBitrate: parseInt(process.env.MAX_BITRATE) || 3000000, // 3Mbps
            maxFramerate: parseFloat(process.env.MAX_FRAMERATE) || 30.0,
          },
        ],
        codecOptions: {
          videoGoogleStartBitrate: parseInt(process.env.START_BITRATE) || 3000, // 3Mbps in kbps
        },
      },
    },

    // Target IP and port for RTP recording
    recording: {
      ip: process.env.RECORDING_IP || "127.0.0.1",

      // GStreamer's sdpdemux only supports RTCP = RTP + 1
      audioPort: parseInt(process.env.AUDIO_PORT) || 5004,
      audioPortRtcp: parseInt(process.env.AUDIO_PORT_RTCP) || 5005,
      videoPort: parseInt(process.env.VIDEO_PORT) || 5006,
      videoPortRtcp: parseInt(process.env.VIDEO_PORT_RTCP) || 5007,
    },
  },
};
