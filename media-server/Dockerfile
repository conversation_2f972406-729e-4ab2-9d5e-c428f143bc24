# Use Node.js 18 LTS as base image
FROM node:18-bullseye

# Install system dependencies required for mediasoup and FFmpeg
RUN apt-get update && apt-get install -y \
    build-essential \
    python3 \
    python3-pip \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install Node.js dependencies
RUN npm ci --only=production

# Copy application code
COPY . .

# Build client bundle
RUN npm run prestart

# Create directory for recordings
RUN mkdir -p /app/recording

# Expose port
EXPOSE 8080

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080

# Start the application
CMD ["npm", "start"]
