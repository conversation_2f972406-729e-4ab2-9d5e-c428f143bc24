const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const mediasoup = require('mediasoup');
const { spawn } = require('child_process');

const CONFIG = process.env.NODE_ENV === 'production'
  ? require('./config.production')
  : require('./config');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
    credentials: true
  }
});

app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/', (req, res) => {
  res.json({
    status: 'ok',
    service: 'media-server',
    timestamp: new Date().toISOString(),
    connections: io.engine.clientsCount
  });
});

// Global mediasoup objects - simplified for direct connections
let worker;
let router;
let webRtcTransport;
let producer;
let audioProducer;
let rtpTransport;
let audioRtpTransport;
let consumer;
let audioConsumer;
let ffmpegProcess;
let keyframeInterval;

// Initialize mediasoup
async function initializeMediasoup() {
  try {
    // Create worker
    worker = await mediasoup.createWorker(CONFIG.mediasoup.worker);
    console.log('✅ Mediasoup worker created [pid:%d]', worker.pid);

    worker.on('died', () => {
      console.error('❌ Mediasoup worker died, exiting...');
      process.exit(1);
    });

    // Create router with both audio and video codecs for RTMP compatibility
    router = await worker.createRouter(CONFIG.mediasoup.router);
    console.log('✅ Mediasoup router created');

    return true;
  } catch (error) {
    console.error('❌ Failed to initialize mediasoup:', error);
    return false;
  }
}

// Socket.io connection handling
io.on('connection', (socket) => {
  console.log('🔌 Client connected:', socket.id);
  console.log('📊 Total connections:', io.engine.clientsCount);

  // Handle WebRTC transport creation
  socket.on('get-transport-info', async () => {
    console.log('📡 Client requested transport info');
    try {
      if (!router) {
        console.error('❌ Router not initialized');
        socket.emit('error', { message: 'Router not initialized' });
        return;
      }

      // Create WebRTC transport
      webRtcTransport = await router.createWebRtcTransport(CONFIG.mediasoup.webrtcTransport);

      const transportInfo = {
        id: webRtcTransport.id,
        iceParameters: webRtcTransport.iceParameters,
        iceCandidates: webRtcTransport.iceCandidates,
        dtlsParameters: webRtcTransport.dtlsParameters,
        routerRtpCapabilities: router.rtpCapabilities,
      };

      socket.emit('transport-info', transportInfo);
      console.log('✅ WebRTC transport created and sent to client');
    } catch (error) {
      console.error('❌ Failed to create transport:', error);
      socket.emit('error', { message: 'Failed to create transport' });
    }
  });

  // Handle transport connection
  socket.on('connect-transport', async ({ dtlsParameters }) => {
    console.log('🔗 Client connecting transport');
    try {
      if (!webRtcTransport) {
        console.error('❌ Transport not created');
        socket.emit('error', { message: 'Transport not created' });
        return;
      }

      await webRtcTransport.connect({ dtlsParameters });
      socket.emit('transport-connected');
      console.log('✅ WebRTC transport connected');
    } catch (error) {
      console.error('❌ Failed to connect transport:', error);
      socket.emit('error', { message: 'Failed to connect transport' });
    }
  });

  // Handle producer creation
  socket.on('create-producer', async ({ kind, rtpParameters }) => {
    console.log('🎬 Client creating producer, kind:', kind);
    try {
      if (!webRtcTransport) {
        console.error('❌ Transport not connected');
        socket.emit('error', { message: 'Transport not connected' });
        return;
      }

      const newProducer = await webRtcTransport.produce({ kind, rtpParameters });

      // Store producers by kind
      if (kind === 'video') {
        producer = newProducer;
        console.log('✅ Video producer created:', producer.id);
      } else if (kind === 'audio') {
        audioProducer = newProducer;
        console.log('✅ Audio producer created:', audioProducer.id);
      }

      socket.emit('producer-created', { producerId: newProducer.id });
      console.log('📊 Producer details:', {
        id: newProducer.id,
        kind: newProducer.kind,
        paused: newProducer.paused,
        rtpParameters: {
          codecs: rtpParameters.codecs?.map(c => ({ mimeType: c.mimeType, clockRate: c.clockRate })),
          headerExtensions: rtpParameters.headerExtensions?.length,
          encodings: rtpParameters.encodings?.length
        }
      });

      // Monitor producer stats
      const statsInterval = setInterval(async () => {
        try {
          if (kind === 'video' && producer) {
            const stats = await producer.getStats();
            console.log('📊 Video producer stats:', stats);
          } else if (kind === 'audio' && audioProducer) {
            const stats = await audioProducer.getStats();
            console.log('📊 Audio producer stats:', stats);
          }
        } catch (error) {
          console.log('⚠️ Failed to get producer stats:', error);
        }
      }, 5000);

      newProducer.on('close', () => {
        clearInterval(statsInterval);
        console.log('🔌 Producer closed:', kind);
      });

      // Auto-start RTMP streaming when video producer is created
      if (kind === 'video') {
        console.log('🎬 Auto-starting RTMP streaming for video producer...');
        try {
          await startRTMPStreaming();
          console.log('✅ RTMP streaming auto-started successfully');
          socket.emit('streaming-status', { isStreaming: true });
        } catch (error) {
          console.error('❌ Failed to auto-start RTMP streaming:', error);
          socket.emit('error', { message: 'Failed to start RTMP streaming' });
        }
      }
    } catch (error) {
      console.error('❌ Failed to create producer:', error);
      socket.emit('error', { message: 'Failed to create producer' });
    }
  });

  // Handle streaming status request
  socket.on('get-streaming-status', () => {
    console.log('📊 Client requested streaming status');
    const isStreaming = ffmpegProcess !== null && !ffmpegProcess.killed;
    socket.emit('streaming-status', { isStreaming });
    console.log('📡 Sent streaming status:', isStreaming);
  });

  socket.on('disconnect', () => {
    console.log('🔌 Client disconnected:', socket.id);
    cleanup();
  });
});

// RTMP Streaming
async function startRTMPStreaming() {
  console.log('🚀 startRTMPStreaming() called');
  try {
    if (!producer || !router) {
      console.log('❌ Missing dependencies - producer:', !!producer, 'router:', !!router);
      throw new Error('Producer or router not available');
    }
    console.log('✅ Dependencies available, proceeding with RTP transport creation...');

    const useAudio = audioProducer !== null && audioProducer !== undefined;
    const useVideo = producer !== null && producer !== undefined;

    console.log('🎵 Audio available:', useAudio, 'Video available:', useVideo);

    // Create audio RTP transport and consumer if audio producer exists
    if (useAudio) {
      audioRtpTransport = await router.createPlainTransport({
        comedia: false,
        rtcpMux: false,
        ...CONFIG.mediasoup.plainTransport,
      });

      await audioRtpTransport.connect({
        ip: CONFIG.mediasoup.recording.ip,
        port: CONFIG.mediasoup.recording.audioPort,
        rtcpPort: CONFIG.mediasoup.recording.audioPortRtcp,
      });

      console.log('✅ Audio RTP transport connected:',
        `${audioRtpTransport.tuple.localIp}:${audioRtpTransport.tuple.localPort} <--> ${audioRtpTransport.tuple.remoteIp}:${audioRtpTransport.tuple.remotePort}`);

      audioConsumer = await audioRtpTransport.consume({
        producerId: audioProducer.id,
        rtpCapabilities: router.rtpCapabilities,
        paused: true,
      });

      console.log('✅ Audio consumer created:', audioConsumer.kind, audioConsumer.type);
    } else {
      console.log('⚠️ No audio producer available, proceeding with video only');
    }

    // Create video RTP transport and consumer
    if (useVideo) {
      rtpTransport = await router.createPlainTransport({
        comedia: false,
        rtcpMux: false,
        ...CONFIG.mediasoup.plainTransport,
      });

      await rtpTransport.connect({
        ip: CONFIG.mediasoup.recording.ip,
        port: CONFIG.mediasoup.recording.videoPort,
        rtcpPort: CONFIG.mediasoup.recording.videoPortRtcp,
      });

      console.log('✅ Video RTP transport connected:',
        `${rtpTransport.tuple.localIp}:${rtpTransport.tuple.localPort} <--> ${rtpTransport.tuple.remoteIp}:${rtpTransport.tuple.remotePort}`);

      consumer = await rtpTransport.consume({
        producerId: producer.id,
        rtpCapabilities: router.rtpCapabilities,
        paused: true,
      });

      console.log('✅ Video consumer created:', consumer.kind, consumer.type);
    }

    // Start FFmpeg process BEFORE resuming consumers (like mediasoup-recording)
    await startFFmpeg();

    // Resume consumers after FFmpeg is ready
    if (useAudio && audioConsumer) {
      console.log('▶️ Resuming audio consumer');
      await audioConsumer.resume();
    }

    if (useVideo && consumer) {
      console.log('▶️ Resuming video consumer');
      await consumer.resume();

      // Start keyframe interval for video
      startKeyframeInterval();
    }

    console.log('✅ RTMP streaming started with audio:', useAudio, 'video:', useVideo);

  } catch (error) {
    console.error('❌ Failed to start RTMP streaming:', error);
  }
}

// Keyframe interval management (like mediasoup-recording)
function startKeyframeInterval() {
  if (keyframeInterval) {
    clearInterval(keyframeInterval);
  }

  keyframeInterval = setInterval(() => {
    if (consumer && !consumer.closed) {
      // Request keyframe from the consumer (not producer)
      consumer.requestKeyFrame()
        .then(() => console.log('🔑 Keyframe requested'))
        .catch(err => console.log('⚠️ Keyframe request failed:', err));
    }
  }, 2000); // Every 2 seconds like mediasoup-recording

  console.log('🔑 Keyframe interval started');
}

function stopKeyframeInterval() {
  if (keyframeInterval) {
    clearInterval(keyframeInterval);
    keyframeInterval = null;
    console.log('🔑 Keyframe interval stopped');
  }
}

// FFmpeg RTMP streaming
async function startFFmpeg() {
  return new Promise((resolve, reject) => {
    const ffmpegPath = require('ffmpeg-static');

    // Fixed RTMP endpoint - replace with your actual RTMP URL
    const rtmpUrl = 'rtmp://live.twitch.tv/app/live_1048713352_TbNczEfLtlt6wRdvxXLBrfqBBQ72Ax';

    // Build FFmpeg arguments like mediasoup-recording
    const useAudio = audioProducer !== null;
    const useVideo = producer !== null;

    let args = [
      '-nostdin',
      '-protocol_whitelist', 'file,rtp,udp',
      '-fflags', '+genpts',
      '-v', 'verbose',  // Add verbose logging
      '-i', `${__dirname}/recording/input-h264.sdp`
    ];

    // Add codec mappings
    if (useAudio) {
      args.push('-map', '0:a:0', '-c:a', 'aac');
    }
    if (useVideo) {
      args.push('-map', '0:v:0', '-c:v', 'copy');
    }

    // Add output format and destination
    args.push('-f', 'flv', '-rtmp_live', 'live', rtmpUrl);

    console.log('🎬 Starting FFmpeg with audio:', useAudio, 'video:', useVideo);
    console.log('🎬 FFmpeg args:', args.join(' '));

    ffmpegProcess = spawn(ffmpegPath, args);

    ffmpegProcess.on('error', (err) => {
      console.error('❌ FFmpeg error:', err);
      reject(err);
    });

    // Add timeout to detect if FFmpeg is stuck
    const ffmpegTimeout = setTimeout(() => {
      console.log('⚠️ FFmpeg timeout - no stream mapping detected after 10 seconds');
      console.log('⚠️ This might indicate RTP packets are not reaching FFmpeg');
      resolve(); // Don't reject, just continue
    }, 10000);

    ffmpegProcess.on('exit', (code, signal) => {
      console.log('🎬 FFmpeg exited with code:', code, 'signal:', signal);
      ffmpegProcess = null;
    });

    ffmpegProcess.stderr.on('data', (data) => {
      const output = data.toString();
      console.log('FFmpeg:', output);

      // Log specific events
      if (output.includes('Input #0')) {
        console.log('📊 FFmpeg detected input stream');
      }
      if (output.includes('Stream mapping:')) {
        console.log('📊 FFmpeg stream mapping established');
      }
      if (output.includes('frame=')) {
        console.log('📊 FFmpeg processing frames');
      }
      if (output.includes('Connection to tcp://')) {
        console.log('📊 FFmpeg connecting to RTMP server');
      }
      if (output.includes('Stream publish started')) {
        console.log('📊 FFmpeg RTMP publish started');
      }

      // Resolve when FFmpeg starts processing
      if (output.includes('Stream mapping:') || output.includes('frame=')) {
        clearTimeout(ffmpegTimeout);
        resolve();
      }
    });

    ffmpegProcess.stdout.on('data', (data) => {
      console.log('FFmpeg stdout:', data.toString());
    });

    // Create SDP file for FFmpeg input
    createSDPFile();
  });
}

function createSDPFile() {
  const useAudio = audioProducer !== null && audioProducer !== undefined;
  const useVideo = producer !== null && producer !== undefined;

  // Create SDP file based on available producers
  let sdpContent = `v=0
o=- 0 0 IN IP4 127.0.0.1
s=-
c=IN IP4 127.0.0.1
t=0 0
`;

  if (useAudio) {
    sdpContent += `m=audio ${CONFIG.mediasoup.recording.audioPort} RTP/AVPF 111
a=rtcp:${CONFIG.mediasoup.recording.audioPortRtcp}
a=rtpmap:111 opus/48000/2
a=fmtp:111 minptime=10;useinbandfec=1
`;
  }

  if (useVideo) {
    sdpContent += `m=video ${CONFIG.mediasoup.recording.videoPort} RTP/AVPF 125
a=rtcp:${CONFIG.mediasoup.recording.videoPortRtcp}
a=rtpmap:125 H264/90000
a=fmtp:125 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f
a=framerate:30
b=AS:3000
`;
  }

  const sdpPath = `${__dirname}/recording/input-h264.sdp`;
  require('fs').writeFileSync(sdpPath, sdpContent);
  console.log('✅ SDP file created with audio:', useAudio, 'video:', useVideo);
  console.log('📊 SDP file path:', sdpPath);
  if (useAudio) {
    console.log('📊 Audio port:', CONFIG.mediasoup.recording.audioPort, 'RTCP:', CONFIG.mediasoup.recording.audioPortRtcp);
  }
  if (useVideo) {
    console.log('📊 Video port:', CONFIG.mediasoup.recording.videoPort, 'RTCP:', CONFIG.mediasoup.recording.videoPortRtcp);
  }
}

// Cleanup function
function cleanup() {
  if (ffmpegProcess) {
    ffmpegProcess.kill('SIGINT');
    ffmpegProcess = null;
  }

  if (producer) {
    producer.close();
    producer = null;
  }

  if (webRtcTransport) {
    webRtcTransport.close();
    webRtcTransport = null;
  }

  if (rtpTransport) {
    rtpTransport.close();
    rtpTransport = null;
  }
}

// Create recording directory
const fs = require('fs');
const recordingDir = `${__dirname}/recording`;
if (!fs.existsSync(recordingDir)) {
  fs.mkdirSync(recordingDir, { recursive: true });
}

// Start server
async function startServer() {
  try {
    const initialized = await initializeMediasoup();
    if (!initialized) {
      process.exit(1);
    }

    const PORT = process.env.MEDIA_SERVER_PORT || process.env.PORT || 8081;
    server.listen(PORT, () => {
      console.log(`🚀 Media Server running on port ${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  cleanup();
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

